完成以下全部需求，不用询问，直接依次完成:

# 完善本AI任务管理系统
1. 完成代码中的TODO的任务
2. 自动化顺序执行任务过程中，可以停止任务执行。
3. 增加任务执行的日志管理功能，可通过progress_callback记录日志
4. 增加项目管理功能，包括：项目名称、工作目录、需求等
  - 4.1 每个项目可以有多个需求，每个需求会调用新TaskManager实现AI任务管理功能。
  
5. 增加UI界面管理，可实现项目管理、需求管理、任务管理。
  - 5.1 项目管理：新增项目、查看项目、删除项目(删除关联需求)
  - 5.2 需求管理：新增需求、查看需求、删除需求(删除关联任务)
  - 5.3 任务管理：任务拆解、查看任务、修改任务、删除任务（需要检查依赖）
  - 5.3.1 任务运行管理：启动任务（可选择自动化执行还是顺序执行）、停止任务、查看任务进度、查看任务日志等
  - 5.3.2 任务可以重置状态，然后可以再次执行
  - 5.3.3.只有pending状态的任务可以修改
  - 5.3.4.任务拆解后，也可以增加新的任务
  - 5.3.5.可以实现快速任务，一句话描述需求，系统自动生成任务并启动运行。
  - 5.3.6.任务可以重新开始，会使用新的会话ID，并总结之前的结果。
6. 可在线预览项目目录的各类文件，包括AI生成的文件。

# 迭代1
完成以下全部功能，不用询问，直接依次完成:

1. "增加项目"的界面使用表单界面，而不是一个一个对话框输入属性。[text](../.claude)
2. TaskManager init方法session_id从self.task_file_name的meta.session_id中获取。
4. 修改当前项目和需求的逻辑：
   - 删除需求管理功能
   - 每个项目只管理一个需求，不需要独立的需求管理功能，把需求直接作为项目属性即可
   - 项目需求以markdown渲染和编辑，编辑框尽量大
5. 项目列表界面改为“列表”形式，而不用表框：
   - 可显示：项目名称、创建时间、更新时间（检查项目目录/.aitest/tasks.json文件的meta数据-可参考/mnt/d/agent/auto-claude-tasks/demo/.taskai/task.json）、任务数量、已完成任务数
   - 选中一个项目可以：查看编辑项目的详情，包括项目的需求，可以选中生成/重新生成任务，可以跳转到任务列表。
   - 选中项目生成任务时，如果已经有任务，提示：已经有任务是否重新生成？如果任务在运行中，则需要中断任务运行。
6. 任务列表是与一个项目关联的：
   - 可显示项目目录/.aitest/tasks.json文件中的各任务详情
   - pending状态的任务可编辑、删除（需检查依赖）
7. 修复错误：
   - AttributeError: 'TaskManager' object has no attribute 'get_task_logs'
   - "GET /requirement/85242ce3-c852-49f4-8ec3-8a2cc1cf8fd6 HTTP/1.1" 500

完成以下全部功能，不用询问:
# 迭代2
1. 项目属性里要包括：工作目录，并可以在界面修改、查看详情。
2. 不要再修改TaskManager的init函数，不要自己生成session_id
3. TaskManager增加listTasks方法，返回所有任务(加载self.task_file_name文件的内容)，项目的任务列表调用这个方法查看任务。
4. 生成任务对话框，增加一个任务数量的输入框，可空。
5. 任务列表页面，点击任务，编辑和查看详情都提示：任务不存在
6. 任务列表页面，点击某个任务，可以运行（需要检查依赖）
7. 任务列表页面，点击某个任务，可查看这个任务的运行日志（可通过progress_callback捕获任务的运行日志）
8. 任务执行、完成等流程后，需要修改任务的状态
9. 解决错误：项目删除时 AttributeError: 'ProjectManager' object has no attribute 'requirements'
10. 任务列表可增加任务，弹出任务框输入新的任务，并可选是否立即执行。

完成以下全部功能，不用询问:
# 迭代3
1. 项目列表增加一个项目工作目录文件预览功能
  - 左侧树型结构的展示项目的文件目录
  - 点击文件，可在右侧预览文件内容，支持java/c/json/markdown/python等类型文件的优化展示
2. 在任何代码里都不要用uuid生成session_id，只能从task_file_name变量的文件里获取
  - task_file_name = 项目工作目录的 "/.taskai/task.json"文件，不要修改这个路径。
3. task_manager.py中任务属性的名称请以文件中/mnt/d/agent/auto-claude-tasks/demo/.taskai/task.json的为准进行修改。
4. 前端UI选中任务，查看日志错误： "GET /api/projects/fd23027e-aaf1-4d2b-ac63-ce93c0d5a354/tasks//logs HTTP/1.1" 405。
  - 通过progress_callback捕获任务的运行日志-大模型的返回消息，参考/mnt/d/agent/auto-claude-tasks/src/claude/claude_agent.py中调用progress_callback输出的内容。
  - 日志存储到data目录，可在界面查看运行日志

# 迭代4
完成以下全部功能，不用询问:

1. 优化任务的LLM交互日志管理功能：
  - LLM日志文件的写入过程参见：task_manager.py的_run_single_task方法
  - 当前所有log_manager.log_event()的日志写入都改用logging的info、warning、error、debug方法写入
  - LLM交互日志的格式可参考文件: data/logs/csdk项目C语言重构/task_1.log
  - UI界面的日志查看以友好格式显示LLM的交互日志：分左右，左侧是LLM的各种类型的输出（Assistant/User/Result/Stream），右侧是用户请求(Request)
  - 任务日志的展示列表中每一条最多显示3行(换行符标识为 "↵ ")，超过3行则显示"...还有n行"，然后点击查看所有行。展开所有行也可以收起。
  - 日志查看页面能需要实时刷新
  - LLM日志管理的功能可以都抽取到log_manager中（不要保留当前log_manager功能，当前log_manager功能都直接使用logging即可）。  

2. 项目的任务列表增加：重置功能，可以把项目的所有任务状态修改为pending状态，并去除result属性，设置meta的session_id属性=None。
3. 任务列表UI增加：任务的更新时间
4. 任务列表UI的任务修改功能，优化为与新增任务类似：表单样式展示任务的全部内容，可修改并保存。
5. 前端UI不要使用render_template渲染，修改为前端UI是纯静态页面，通过ajax请求后端REST API交互数据。
6. 解决以下错误：
  - 文件预览功能错误：   加载文件列表失败: 未知错误  "GET /api/projects/1758977931726/files?path= HTTP/1.1" 500

  问题1：
  1. 界面的重构基于当前的templates目录下的所有html文件，不是在static目录下另外创建新的UI界面。
  2. 完善之前的功能要求，通过浏览器检查功能是否正常。

  改进1:
  完成以下全部功能，不用询问:
  1. templates/project_tasks.html的日志查看修改为查看任务的LLM交互日志（src/log_manager.py的LLMLogManager）
  2. LLM交互日志采用独立的页面，日志格式参考文件: data/logs/俄罗斯方块/task_1.log
     - LLM日志页面，每条日志支持多行展示：把换行符↵ 替换为 在界面可以换行的样式
     - 优化展示样式，根据不同的类型(Request/Assistant/User/Result/Stream)显示不同的颜色(浅色系)
     - 每一条日志默认最多显示3行(换行符标识为 "↵ ")，超过3行则显示"...还有n行"，然后点击查看所有行。展开所有行也可以收起。
     - 在LLM交互页面，支持实时刷新：只获取最新更新的日志，不要拉取整个日志。
  3. templates/project_tasks.html任务列表页面，编辑任务应该弹出任务编辑的表单进行编辑，可参考任务详情。