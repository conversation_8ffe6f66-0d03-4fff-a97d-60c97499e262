{% extends "base.html" %}

{% block title %}{{ project.name }} - 项目详情{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <div>
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="{{ url_for('projects') }}">项目</a></li>
                <li class="breadcrumb-item active">{{ project.name }}</li>
            </ol>
        </nav>
        <h1 class="h2">{{ project.name }}</h1>
        {% if project.description %}
            <p class="text-muted">{{ project.description }}</p>
        {% endif %}
    </div>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <button type="button" class="btn btn-primary" onclick="createTask()">
                <i class="fas fa-plus"></i> 新建任务
            </button>
            <button type="button" class="btn btn-warning" onclick="resetTasks()">
                <i class="fas fa-undo"></i> 重置任务
            </button>
            <button type="button" class="btn btn-outline-secondary" onclick="location.reload()">
                <i class="fas fa-sync-alt"></i> 刷新
            </button>
            <a href="{{ url_for('file_browser', project_id=project.project_id) }}" class="btn btn-outline-info">
                <i class="fas fa-folder-open"></i> 文件浏览
            </a>
        </div>
    </div>
</div>

<!-- 项目统计 -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h5 class="card-title text-info">{{ summary.total_tasks }}</h5>
                <p class="card-text">总任务数</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h5 class="card-title text-success">{{ summary.completed_tasks }}</h5>
                <p class="card-text">已完成</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h5 class="card-title text-warning">{{ summary.pending_tasks }}</h5>
                <p class="card-text">待执行</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h5 class="card-title text-danger">{{ summary.failed_tasks }}</h5>
                <p class="card-text">失败</p>
            </div>
        </div>
    </div>
</div>

<!-- 任务列表 -->
<div class="card">
    <div class="card-header">
        <h5 class="mb-0">项目任务</h5>
    </div>
    <div class="card-body">
        {% if tasks %}
            <div class="row" id="tasks-container">
                {% for task in tasks %}
                <div class="col-lg-6 mb-3">
                    <div class="card task-card">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="mb-0">{{ task.title }}</h6>
                                <small class="text-muted">{{ task.id }}</small>
                            </div>
                            <div>
                                {{ getStatusBadge(task.status)|safe }}
                                {{ getPriorityBadge(task.priority)|safe }}
                            </div>
                        </div>
                        <div class="card-body">
                            <p class="card-text">{{ task.description[:100] }}{% if task.description|length > 100 %}...{% endif %}</p>

                            <div class="row mb-2">
                                <div class="col-6">
                                    <small class="text-muted">
                                        <i class="fas fa-calendar-plus"></i> 创建: {{ task.created_at|formatDateTime }}
                                    </small>
                                </div>
                                <div class="col-6">
                                    <small class="text-muted">
                                        <i class="fas fa-calendar-check"></i> 更新: {{ task.updated_at|formatDateTime }}
                                    </small>
                                </div>
                            </div>

                            {% if task.result %}
                            <div class="mb-2">
                                <small class="text-success">
                                    <i class="fas fa-check-circle"></i> 结果: {{ task.result[:50] }}{% if task.result|length > 50 %}...{% endif %}
                                </small>
                            </div>
                            {% endif %}

                            <div class="d-flex justify-content-between align-items-center">
                                <div class="btn-group btn-group-sm">
                                    <button class="btn btn-outline-primary" onclick="editTask('{{ task.id }}')"
                                            {% if task.status != 'pending' %}disabled{% endif %}>
                                        <i class="fas fa-edit"></i> 编辑
                                    </button>
                                    <button class="btn btn-outline-success" onclick="runTask('{{ task.id }}')"
                                            {% if task.status == 'completed' %}disabled{% endif %}>
                                        <i class="fas fa-play"></i> 执行
                                    </button>
                                    <button class="btn btn-outline-info" onclick="viewLogs('{{ task.id }}')">
                                        <i class="fas fa-file-text"></i> 日志
                                    </button>
                                    <button class="btn btn-outline-secondary" onclick="viewLLMLogs('{{ task.id }}')">
                                        <i class="fas fa-comments"></i> LLM日志
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                {% endfor %}
            </div>
        {% else %}
            <div class="text-center py-4">
                <i class="fas fa-tasks fa-3x text-muted mb-3"></i>
                <p class="text-muted">还没有任务，<a href="#" onclick="createTask()">创建第一个任务</a></p>
            </div>
        {% endif %}
    </div>
</div>
{% endblock %}

{% block modals %}
<!-- 新建任务模态框 -->
<div class="modal fade" id="createTaskModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="taskModalTitle">新建任务</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="taskForm">
                    <input type="hidden" id="taskId">
                    <div class="mb-3">
                        <label for="taskTitle" class="form-label">任务标题</label>
                        <input type="text" class="form-control" id="taskTitle" required>
                    </div>
                    <div class="mb-3">
                        <label for="taskDescription" class="form-label">任务描述</label>
                        <textarea class="form-control" id="taskDescription" rows="3" required></textarea>
                    </div>
                    <div class="mb-3">
                        <label for="taskDetails" class="form-label">任务详情</label>
                        <textarea class="form-control" id="taskDetails" rows="4"></textarea>
                    </div>
                    <div class="mb-3">
                        <label for="taskPriority" class="form-label">优先级</label>
                        <select class="form-select" id="taskPriority">
                            <option value="low">低</option>
                            <option value="medium" selected>中</option>
                            <option value="high">高</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="taskTestStrategy" class="form-label">测试策略</label>
                        <textarea class="form-control" id="taskTestStrategy" rows="3"></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" onclick="saveTask()">保存</button>
            </div>
        </div>
    </div>
</div>

<!-- 任务日志查看模态框 -->
<div class="modal fade" id="taskLogsModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">任务日志</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div id="taskLogsContent" class="log-container">
                    <!-- 日志内容将在这里显示 -->
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-primary" onclick="refreshTaskLogs()">
                    <i class="fas fa-sync-alt"></i> 刷新
                </button>
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
            </div>
        </div>
    </div>
</div>

<!-- LLM日志查看模态框 -->
<div class="modal fade" id="llmLogsModal" tabindex="-1">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">LLM交互日志</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-md-8">
                        <h6>LLM输出</h6>
                        <div id="llmLogsOutput" class="log-container">
                            <!-- LLM输出日志 -->
                        </div>
                    </div>
                    <div class="col-md-4">
                        <h6>用户请求</h6>
                        <div id="llmLogsRequests" class="log-container">
                            <!-- 用户请求日志 -->
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-primary" onclick="refreshLLMLogs()">
                    <i class="fas fa-sync-alt"></i> 刷新
                </button>
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    let currentTaskId = null;

    function createTask() {
        currentTaskId = null;
        $('#taskModalTitle').text('新建任务');
        $('#taskForm')[0].reset();
        $('#taskId').val('');
        $('#createTaskModal').modal('show');
    }

    function saveTask() {
        const taskData = {
            title: $('#taskTitle').val().trim(),
            description: $('#taskDescription').val().trim(),
            details: $('#taskDetails').val().trim(),
            priority: $('#taskPriority').val(),
            testStrategy: $('#taskTestStrategy').val().trim()
        };

        if (!taskData.title || !taskData.description) {
            showAlert('请填写完整的任务信息', 'warning');
            return;
        }

        const url = currentTaskId ?
            `/api/projects/{{ project.project_id }}/tasks/${currentTaskId}` :
            `/api/projects/{{ project.project_id }}/tasks`;
        const method = currentTaskId ? 'PUT' : 'POST';

        $.ajax({
            url: url,
            method: method,
            contentType: 'application/json',
            data: JSON.stringify(taskData),
            success: function(response) {
                if (response.success || !response.error) {
                    showAlert(currentTaskId ? '任务更新成功！' : '任务创建成功！', 'success');
                    $('#createTaskModal').modal('hide');
                    setTimeout(() => {
                        location.reload();
                    }, 1000);
                } else {
                    showAlert((currentTaskId ? '任务更新失败: ' : '任务创建失败: ') + (response.error || response.message), 'danger');
                }
            },
            error: function(xhr) {
                const response = xhr.responseJSON || {};
                showAlert((currentTaskId ? '任务更新失败: ' : '任务创建失败: ') + (response.error || response.message || '未知错误'), 'danger');
            }
        });
    }

    function editTask(taskId) {
        currentTaskId = taskId;

        // 获取任务信息
        $.ajax({
            url: `/api/projects/{{ project.project_id }}/tasks`,
            method: 'GET',
            success: function(tasks) {
                const task = tasks.find(t => t.id === taskId);
                if (!task) {
                    showAlert('任务不存在', 'danger');
                    return;
                }

                $('#taskModalTitle').text('编辑任务');
                $('#taskId').val(task.id);
                $('#taskTitle').val(task.title);
                $('#taskDescription').val(task.description);
                $('#taskDetails').val(task.details || '');
                $('#taskPriority').val(task.priority);
                $('#taskTestStrategy').val(task.testStrategy || '');

                $('#createTaskModal').modal('show');
            },
            error: function() {
                showAlert('获取任务信息失败', 'danger');
            }
        });
    }

    function runTask(taskId) {
        if (confirm('确定要执行这个任务吗？')) {
            showAlert('任务开始执行，请稍候...', 'info');

            $.ajax({
                url: `/api/projects/{{ project.project_id }}/tasks/${taskId}/run`,
                method: 'POST',
                success: function(response) {
                    if (response.success) {
                        showAlert('任务已开始执行！', 'success');

                        // 定期刷新任务状态
                        const interval = setInterval(() => {
                            location.reload();
                        }, 3000);

                        // 10秒后停止自动刷新
                        setTimeout(() => {
                            clearInterval(interval);
                        }, 30000);
                    } else {
                        showAlert('任务执行失败: ' + (response.message || '未知错误'), 'danger');
                    }
                },
                error: function(xhr) {
                    const response = xhr.responseJSON || {};
                    showAlert('任务执行失败: ' + (response.message || '未知错误'), 'danger');
                }
            });
        }
    }

    function viewLogs(taskId) {
        currentTaskId = taskId;

        $.ajax({
            url: `/api/projects/{{ project.project_id }}/tasks/${taskId}/logs`,
            method: 'GET',
            success: function(response) {
                if (response.logs) {
                    const logsHtml = response.logs.map(log =>
                        `<div class="log-entry">${escapeHtml(log)}</div>`
                    ).join('');
                    $('#taskLogsContent').html(logsHtml);
                } else {
                    $('#taskLogsContent').html('<p class="text-muted">暂无日志记录</p>');
                }
                $('#taskLogsModal').modal('show');
            },
            error: function() {
                showAlert('获取任务日志失败', 'danger');
            }
        });
    }

    function refreshTaskLogs() {
        if (currentTaskId) {
            viewLogs(currentTaskId);
        }
    }

    function viewLLMLogs(taskId) {
        currentTaskId = taskId;
        refreshLLMLogs();
        $('#llmLogsModal').modal('show');
    }

    function refreshLLMLogs() {
        if (!currentTaskId) return;

        $.ajax({
            url: `/api/projects/{{ project.project_id }}/tasks/${currentTaskId}/llm-logs`,
            method: 'GET',
            success: function(response) {
                if (response.success && response.logs) {
                    const outputLogs = [];
                    const requestLogs = [];

                    response.logs.forEach(log => {
                        const logHtml = `
                            <div class="log-entry mb-2 p-2 border-bottom">
                                <small class="text-muted">[${log.timestamp}] ${log.progress}</small>
                                <div>${escapeHtml(log.content)}</div>
                            </div>
                        `;

                        if (log.log_type === 'Request') {
                            requestLogs.push(logHtml);
                        } else {
                            outputLogs.push(logHtml);
                        }
                    });

                    $('#llmLogsOutput').html(
                        outputLogs.length > 0 ? outputLogs.join('') : '<p class="text-muted">暂无输出日志</p>'
                    );
                    $('#llmLogsRequests').html(
                        requestLogs.length > 0 ? requestLogs.join('') : '<p class="text-muted">暂无请求日志</p>'
                    );
                } else {
                    $('#llmLogsOutput').html('<p class="text-muted">获取日志失败</p>');
                    $('#llmLogsRequests').html('<p class="text-muted">获取日志失败</p>');
                }
            },
            error: function() {
                $('#llmLogsOutput').html('<p class="text-danger">获取日志失败</p>');
                $('#llmLogsRequests').html('<p class="text-danger">获取日志失败</p>');
            }
        });
    }

    function resetTasks() {
        if (confirm('确定要重置所有任务吗？这将清除所有任务的执行结果并将状态重置为待执行。')) {
            showAlert('正在重置任务，请稍候...', 'info');

            $.ajax({
                url: `/api/projects/{{ project.project_id }}/tasks/reset`,
                method: 'POST',
                success: function(response) {
                    if (response.success) {
                        showAlert('任务重置成功！', 'success');
                        setTimeout(() => {
                            location.reload();
                        }, 1000);
                    } else {
                        showAlert('任务重置失败: ' + (response.error || response.message || '未知错误'), 'danger');
                    }
                },
                error: function(xhr) {
                    const response = xhr.responseJSON || {};
                    showAlert('任务重置失败: ' + (response.error || response.message || '未知错误'), 'danger');
                }
            });
        }
    }

    function escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }
</script>
{% endblock %}
