#!/usr/bin/env python3
"""
AI任务管理系统Web界面
"""

import os
import json
import threading
from datetime import datetime
from flask import Flask, render_template, request, jsonify, redirect, url_for, flash, send_file
from werkzeug.utils import secure_filename

try:
    from .project_manager import ProjectManager
    from .task_manager import TaskManager
    from .log_manager import TaskLogManager
    from .file_manager import FileManager
except ImportError:
    from project_manager import ProjectManager
    from task_manager import TaskManager
    from log_manager import TaskLogManager
    from file_manager import FileManager

# 获取当前文件所在目录
current_dir = os.path.dirname(os.path.abspath(__file__))
# 构建模板和静态文件目录路径
template_dir = os.path.join(current_dir, '..', 'templates')
static_dir = os.path.join(current_dir, '..', 'static')

# 实例化Flask应用并指定模板和静态文件目录
app = Flask(__name__, 
            template_folder=template_dir,
            static_folder=static_dir)
app.secret_key = 'ai-task-manager-secret-key-2025'

# 全局变量
project_manager = None
running_tasks = {}  # 存储正在运行的任务
task_threads = {}   # 存储任务线程

# 模板过滤器和函数
@app.template_filter('formatDateTime')
def format_datetime(value):
    """格式化日期时间"""
    if isinstance(value, str):
        try:
            dt = datetime.fromisoformat(value.replace('Z', '+00:00'))
            return dt.strftime('%Y-%m-%d %H:%M:%S')
        except:
            return value
    return value

@app.template_global()
def getStatusBadge(status):
    """获取状态徽章HTML"""
    status_map = {
        'pending': '<span class="badge bg-secondary">待处理</span>',
        'in_progress': '<span class="badge bg-primary">进行中</span>',
        'running': '<span class="badge bg-info">运行中</span>',
        'completed': '<span class="badge bg-success">已完成</span>',
        'failed': '<span class="badge bg-danger">失败</span>',
        'cancelled': '<span class="badge bg-warning">已取消</span>',
        'stopped': '<span class="badge bg-warning">已停止</span>'
    }
    return status_map.get(status, f'<span class="badge bg-light">{status}</span>')

@app.template_global()
def getPriorityBadge(priority):
    """获取优先级徽章HTML"""
    priority_map = {
        'high': '<span class="badge bg-danger">高</span>',
        'medium': '<span class="badge bg-warning">中</span>',
        'low': '<span class="badge bg-info">低</span>'
    }
    return priority_map.get(priority, f'<span class="badge bg-light">{priority}</span>')

@app.route('/')
def index():
    """首页"""
    projects = project_manager.list_projects()
    return render_template('index.html', projects=projects)

@app.route('/projects')
def projects():
    """项目列表页面"""
    projects = project_manager.list_projects()

    # 为每个项目添加统计信息
    projects_with_stats = []
    for project in projects:
        summary = project_manager.get_project_summary(project.project_id)
        project_dict = project.to_dict()
        project_dict.update({
            'total_tasks': summary.get('total_tasks', 0),
            'completed_tasks': summary.get('completed_tasks', 0)
        })
        projects_with_stats.append(type('Project', (), project_dict)())

    return render_template('projects.html', projects=projects_with_stats)

@app.route('/project/<project_id>')
def project_detail(project_id):
    """项目详情页面"""
    project = project_manager.get_project(project_id)
    if not project:
        flash('项目不存在', 'error')
        return redirect(url_for('projects'))

    summary = project_manager.get_project_summary(project_id)

    # 获取任务列表
    tasks = []
    task_manager = project_manager.get_task_manager(project_id)
    if task_manager:
        try:
            tasks = task_manager.list_tasks()
        except Exception as e:
            flash(f'加载任务失败: {e}', 'error')

    return render_template('project_detail.html',
                         project=project,
                         summary=summary,
                         tasks=tasks)

@app.route('/project/<project_id>/files')
def file_browser(project_id):
    """文件浏览器页面"""
    project = project_manager.get_project(project_id)
    if not project:
        flash('项目不存在', 'error')
        return redirect(url_for('projects'))

    return render_template('file_browser.html', project=project)

def init_app(data_dir="data"):
    """初始化应用"""
    global project_manager
    project_manager = ProjectManager(data_dir)
    
    # 创建模板目录
    template_dir = os.path.join(os.path.dirname(__file__), '..', 'templates')
    static_dir = os.path.join(os.path.dirname(__file__), '..', 'static')
    os.makedirs(template_dir, exist_ok=True)
    os.makedirs(static_dir, exist_ok=True)

@app.route('/project/<project_id>/tasks')
def project_tasks(project_id):
    """项目任务列表页面"""
    project = project_manager.get_project(project_id)
    if not project:
        flash('项目不存在', 'error')
        return redirect(url_for('projects'))

    # 获取任务信息
    tasks = []
    task_manager = project_manager.get_task_manager(project_id)
    if task_manager:
        try:
            tasks = task_manager.list_tasks()
        except Exception as e:
            flash(f'加载任务失败: {e}', 'error')

    return render_template('project_tasks.html', project=project, tasks=tasks)

@app.route('/project/<project_id>/files')
def project_files(project_id):
    """项目文件预览页面"""
    project = project_manager.get_project(project_id)
    if not project:
        flash('项目不存在', 'error')
        return redirect(url_for('projects'))

    return render_template('project_files.html', project=project)

# API路由
@app.route('/api/projects', methods=['GET', 'POST'])
def api_projects():
    """项目API"""
    if request.method == 'GET':
        projects = project_manager.list_projects()
        return jsonify([project.to_dict() for project in projects])
    
    elif request.method == 'POST':
        data = request.get_json()
        try:
            project_id = project_manager.create_project(
                name=data['name'],
                work_dir=data['work_dir'],
                description=data.get('description', ''),
                requirement=data.get('requirement', '')
            )
            return jsonify({'success': True, 'project_id': project_id})
        except Exception as e:
            return jsonify({'success': False, 'message': str(e)}), 400

@app.route('/api/projects/<project_id>', methods=['GET', 'PUT', 'DELETE'])
def api_project(project_id):
    """单个项目API"""
    if request.method == 'GET':
        project = project_manager.get_project(project_id)
        if not project:
            return jsonify({'error': '项目不存在'}), 404
        return jsonify(project.to_dict())

    elif request.method == 'PUT':
        data = request.get_json()
        success = project_manager.update_project(
            project_id,
            name=data.get('name'),
            description=data.get('description'),
            work_dir=data.get('work_dir'),
            requirement=data.get('requirement')
        )
        if success:
            return jsonify({'success': True})
        else:
            return jsonify({'success': False, 'message': '项目不存在'}), 404
    
    elif request.method == 'DELETE':
        success = project_manager.delete_project(project_id, delete_files=False)
        if success:
            return jsonify({'success': True})
        else:
            return jsonify({'success': False, 'message': '项目不存在'}), 404

@app.route('/api/projects/<project_id>/summary', methods=['GET'])
def api_project_summary(project_id):
    """获取项目摘要信息API"""
    project = project_manager.get_project(project_id)
    if not project:
        return jsonify({'error': '项目不存在'}), 404

    try:
        summary = project_manager.get_project_summary(project_id)
        return jsonify(summary)
    except Exception as e:
        return jsonify({'error': f'获取项目摘要失败: {e}'}), 500

@app.route('/api/projects/<project_id>/generate_tasks', methods=['POST'])
def api_generate_project_tasks(project_id):
    """为项目生成任务"""
    try:
        data = request.get_json() or {}
        num_tasks = data.get('num_tasks')

        result = project_manager.generate_tasks_for_project(project_id, num_tasks)
        return jsonify(result)
    except Exception as e:
        return jsonify({'success': False, 'message': str(e)}), 500

@app.route('/api/projects/<project_id>/run_tasks', methods=['POST'])
def api_run_project_tasks(project_id):
    """运行项目任务"""
    try:
        data = request.get_json() or {}
        parallel_mode = data.get('parallel_mode', False)

        result = project_manager.run_project_tasks(project_id, parallel_mode)
        return jsonify(result)
    except Exception as e:
        return jsonify({'success': False, 'message': str(e)}), 500

# 删除了project_tasks和project_files路由

@app.route('/api/projects/<project_id>/tasks', methods=['GET'])
def api_project_tasks(project_id):
    """获取项目任务列表API"""
    project = project_manager.get_project(project_id)
    if not project:
        return jsonify({'error': '项目不存在'}), 404

    # 获取任务信息
    tasks = []
    task_manager = project_manager.get_task_manager(project_id)
    if task_manager:
        try:
            tasks = task_manager.list_tasks()
        except Exception as e:
            return jsonify({'error': f'加载任务失败: {e}'}), 500

    return jsonify({'tasks': tasks})

@app.route('/api/projects/<project_id>/tasks/<task_id>', methods=['PUT', 'DELETE'])
def api_project_task(project_id, task_id):
    """项目任务编辑和删除API"""
    project = project_manager.get_project(project_id)
    if not project:
        return jsonify({'error': '项目不存在'}), 404

    task_manager = project_manager.get_task_manager(project_id)
    if not task_manager:
        return jsonify({'error': '无法获取TaskManager'}), 404

    if request.method == 'PUT':
        # 编辑任务
        data = request.get_json()
        try:
            # 加载现有任务
            if not os.path.exists(task_manager.task_file_name):
                return jsonify({'error': '任务文件不存在'}), 404

            with open(task_manager.task_file_name, 'r', encoding='utf-8') as f:
                tasks_data = json.load(f)

            tasks = tasks_data.get("tasks", [])

            # 查找并更新任务
            task_found = False
            for task in tasks:
                if task.get('task_id') == task_id:
                    if data.get('title'):
                        task['title'] = data['title']
                    if data.get('description'):
                        task['description'] = data['description']
                    if data.get('priority'):
                        task['priority'] = data['priority']
                    task['updated_at'] = datetime.now().isoformat()
                    task_found = True
                    break

            if not task_found:
                return jsonify({'error': '任务不存在'}), 404

            # 保存更新后的任务
            with open(task_manager.task_file_name, 'w', encoding='utf-8') as f:
                json.dump(tasks_data, f, indent=2, ensure_ascii=False)

            return jsonify({'success': True})

        except Exception as e:
            return jsonify({'error': f'更新任务失败: {e}'}), 500

    elif request.method == 'DELETE':
        # 删除任务
        try:
            # 加载现有任务
            if not os.path.exists(task_manager.task_file_name):
                return jsonify({'error': '任务文件不存在'}), 404

            with open(task_manager.task_file_name, 'r', encoding='utf-8') as f:
                tasks_data = json.load(f)

            tasks = tasks_data.get("tasks", [])

            # 查找要删除的任务
            task_to_delete = None
            for task in tasks:
                if task.get('task_id') == task_id:
                    task_to_delete = task
                    break

            if not task_to_delete:
                return jsonify({'error': '任务不存在'}), 404

            # 检查是否有其他任务依赖此任务
            dependent_tasks = []
            for task in tasks:
                if task.get('dependencies') and task_id in task['dependencies']:
                    dependent_tasks.append(task['title'])

            if dependent_tasks:
                return jsonify({
                    'error': f'无法删除任务，以下任务依赖此任务: {", ".join(dependent_tasks)}'
                }), 400

            # 删除任务
            tasks.remove(task_to_delete)

            # 保存更新后的任务
            with open(task_manager.task_file_name, 'w', encoding='utf-8') as f:
                json.dump(tasks_data, f, indent=2, ensure_ascii=False)

            return jsonify({'success': True})

        except Exception as e:
            return jsonify({'error': f'删除任务失败: {e}'}), 500

@app.route('/api/projects/<project_id>/tasks/<task_id>/run', methods=['POST'])
def api_run_single_task(project_id, task_id):
    """运行单个任务API"""
    project = project_manager.get_project(project_id)
    if not project:
        return jsonify({'error': '项目不存在'}), 404

    try:
        result = project_manager.run_single_task(project_id, task_id)
        return jsonify(result)
    except Exception as e:
        return jsonify({'error': f'运行任务失败: {e}'}), 500

@app.route('/api/projects/<project_id>/tasks/<task_id>/logs', methods=['GET'])
def api_get_task_logs(project_id, task_id):
    """获取任务日志API"""
    project = project_manager.get_project(project_id)
    if not project:
        return jsonify({'error': '项目不存在'}), 404

    try:
        logs = project_manager.get_task_logs(project_id, task_id)
        return jsonify({'logs': logs})
    except Exception as e:
        return jsonify({'error': f'获取日志失败: {e}'}), 500

@app.route('/api/projects/<project_id>/tasks/<task_id>/llm-logs', methods=['GET'])
def api_get_task_llm_logs(project_id, task_id):
    """获取任务LLM交互日志API"""
    project = project_manager.get_project(project_id)
    if not project:
        return jsonify({'error': '项目不存在'}), 404

    try:
        from log_manager import get_task_llm_logs
        limit = request.args.get('limit', 100, type=int)
        logs = get_task_llm_logs(project.name, task_id, limit)

        return jsonify({
            'success': True,
            'logs': logs
        })
    except Exception as e:
        logging.error(f"获取LLM日志失败: {e}")
        return jsonify({'error': f'获取LLM日志失败: {e}'}), 500

@app.route('/api/projects/<project_id>/tasks', methods=['POST'])
def api_add_task(project_id):
    """添加新任务API"""
    project = project_manager.get_project(project_id)
    if not project:
        return jsonify({'error': '项目不存在'}), 404

    data = request.get_json()
    try:
        result = project_manager.add_task_to_project(
            project_id,
            title=data.get('title'),
            description=data.get('description', ''),
            priority=data.get('priority', 'medium'),
            dependencies=data.get('dependencies', [])
        )

        # 如果需要立即执行
        if result.get('success') and data.get('execute_immediately'):
            task_id = result['task_id']
            run_result = project_manager.run_single_task(project_id, task_id)
            result['execution_result'] = run_result

        return jsonify(result)
    except Exception as e:
        return jsonify({'error': f'添加任务失败: {e}'}), 500

@app.route('/api/projects/<project_id>/tasks/<task_id>', methods=['PUT'])
def api_update_task(project_id, task_id):
    """更新任务API"""
    project = project_manager.get_project(project_id)
    if not project:
        return jsonify({'error': '项目不存在'}), 404

    data = request.get_json()
    try:
        result = project_manager.update_project_task(
            project_id,
            task_id,
            title=data.get('title'),
            description=data.get('description', ''),
            priority=data.get('priority', 'medium'),
            dependencies=data.get('dependencies', []),
            details=data.get('details', ''),
            testStrategy=data.get('testStrategy', '')
        )

        return jsonify(result)
    except Exception as e:
        return jsonify({'error': f'更新任务失败: {e}'}), 500

@app.route('/api/projects/<project_id>/tasks/reset', methods=['POST'])
def api_reset_project_tasks(project_id):
    """重置项目所有任务API"""
    project = project_manager.get_project(project_id)
    if not project:
        return jsonify({'error': '项目不存在'}), 404

    try:
        success = project_manager.reset_project_tasks(project_id)
        if success:
            return jsonify({'success': True, 'message': '任务重置成功'})
        else:
            return jsonify({'error': '任务重置失败'}), 500
    except Exception as e:
        return jsonify({'error': f'重置任务失败: {e}'}), 500

@app.route('/api/projects/<project_id>/files', methods=['GET'])
def api_get_project_files(project_id):
    """获取项目文件树API"""
    project = project_manager.get_project(project_id)
    if not project:
        return jsonify({'error': '项目不存在'}), 404

    try:
        work_dir = project.work_dir
        if not os.path.exists(work_dir):
            return jsonify({'files': []})

        def build_file_tree(path, base_path):
            """构建文件树"""
            items = []
            try:
                for item in sorted(os.listdir(path)):
                    if item.startswith('.'):
                        continue

                    item_path = os.path.join(path, item)
                    relative_path = os.path.relpath(item_path, base_path)

                    if os.path.isdir(item_path):
                        items.append({
                            'name': item,
                            'type': 'directory',
                            'path': relative_path,
                            'children': build_file_tree(item_path, base_path)
                        })
                    else:
                        items.append({
                            'name': item,
                            'type': 'file',
                            'path': relative_path,
                            'size': os.path.getsize(item_path)
                        })
            except PermissionError:
                pass

            return items

        files = build_file_tree(work_dir, work_dir)
        return jsonify({'files': files})

    except Exception as e:
        return jsonify({'error': f'获取文件列表失败: {e}'}), 500

@app.route('/api/projects/<project_id>/files/content', methods=['GET'])
def api_get_file_content(project_id):
    """获取文件内容API"""
    project = project_manager.get_project(project_id)
    if not project:
        return jsonify({'error': '项目不存在'}), 404

    file_path = request.args.get('path')
    if not file_path:
        return jsonify({'error': '文件路径不能为空'}), 400

    try:
        full_path = os.path.join(project.work_dir, file_path)

        # 安全检查：确保文件在项目目录内
        if not os.path.abspath(full_path).startswith(os.path.abspath(project.work_dir)):
            return jsonify({'error': '无效的文件路径'}), 400

        if not os.path.exists(full_path) or not os.path.isfile(full_path):
            return jsonify({'error': '文件不存在'}), 404

        # 检查文件大小（限制为1MB）
        if os.path.getsize(full_path) > 1024 * 1024:
            return jsonify({'error': '文件过大，无法预览'}), 400

        # 尝试读取文件内容
        try:
            with open(full_path, 'r', encoding='utf-8') as f:
                content = f.read()
            encoding = 'utf-8'
        except UnicodeDecodeError:
            try:
                with open(full_path, 'r', encoding='gbk') as f:
                    content = f.read()
                encoding = 'gbk'
            except UnicodeDecodeError:
                return jsonify({'error': '无法解码文件内容'}), 400

        # 获取文件扩展名
        _, ext = os.path.splitext(file_path)
        ext = ext.lower()

        return jsonify({
            'content': content,
            'encoding': encoding,
            'extension': ext,
            'size': os.path.getsize(full_path),
            'path': file_path
        })

    except Exception as e:
        return jsonify({'error': f'读取文件失败: {e}'}), 500

# 任务管理API
@app.route('/api/tasks', methods=['POST'])
def api_create_task():
    """创建任务API"""
    data = request.get_json()
    try:
        req_id = data['req_id']
        task_manager = project_manager.get_task_manager(req_id)
        if not task_manager:
            return jsonify({'success': False, 'message': '无法获取TaskManager'}), 404

        # 创建任务
        task_id = task_manager.add_task(
            title=data['title'],
            description=data['description'],
            details=data.get('details', ''),
            priority=data.get('priority', 'medium'),
            dependencies=data.get('dependencies', []),
            test_strategy=data.get('testStrategy', '')
        )

        return jsonify({'success': True, 'task_id': task_id})
    except Exception as e:
        return jsonify({'success': False, 'message': str(e)}), 400

@app.route('/api/tasks/<int:task_id>', methods=['GET', 'PUT', 'DELETE'])
def api_task_detail(task_id):
    """任务详情API"""
    # 这里需要从请求中获取req_id，或者从任务ID反向查找
    # 暂时返回错误，需要完善TaskManager的接口
    return jsonify({'success': False, 'message': '功能正在开发中'}), 501

# 文件管理API
@app.route('/api/projects/<project_id>/files')
def api_list_files(project_id):
    """获取项目文件列表"""
    try:
        project = project_manager.get_project(project_id)
        if not project:
            return jsonify({'success': False, 'message': '项目不存在'}), 404

        path = request.args.get('path', '')

        # 检查项目目录是否存在
        if not os.path.exists(project.directory):
            return jsonify({'success': False, 'message': f'项目目录不存在: {project.directory}'}), 404

        file_manager = FileManager(project.directory)
        files = file_manager.list_files(path)

        return jsonify({
            'success': True,
            'files': [file.to_dict() for file in files]
        })

    except Exception as e:
        logging.error(f"获取文件列表失败: {e}")
        return jsonify({'success': False, 'message': f'获取文件列表失败: {str(e)}'}), 500

@app.route('/api/projects/<project_id>/files/preview')
def api_preview_file(project_id):
    """预览文件内容"""
    try:
        project = project_manager.get_project(project_id)
        if not project:
            return jsonify({'success': False, 'message': '项目不存在'})

        path = request.args.get('path', '')
        if not path:
            return jsonify({'success': False, 'message': '文件路径不能为空'})

        file_manager = FileManager(project.directory)
        result = file_manager.get_file_content(path)

        if result is None:
            return jsonify({'success': False, 'message': '文件不存在或无法读取'})

        return jsonify(result)

    except Exception as e:
        return jsonify({'success': False, 'message': str(e)})

@app.route('/api/projects/<project_id>/files/download')
def api_download_file(project_id):
    """下载文件"""
    try:
        project = project_manager.get_project(project_id)
        if not project:
            return jsonify({'success': False, 'message': '项目不存在'})

        path = request.args.get('path', '')
        if not path:
            return jsonify({'success': False, 'message': '文件路径不能为空'})

        file_manager = FileManager(project.directory)
        file_path = file_manager.get_file_path(path)

        if not file_path:
            return jsonify({'success': False, 'message': '文件不存在'})

        filename = os.path.basename(path)
        return send_file(file_path, as_attachment=True, download_name=filename)

    except Exception as e:
        return jsonify({'success': False, 'message': str(e)})

@app.route('/api/projects/<project_id>/files/stats')
def api_file_stats(project_id):
    """获取项目文件统计"""
    try:
        project = project_manager.get_project(project_id)
        if not project:
            return jsonify({'success': False, 'message': '项目不存在'})

        file_manager = FileManager(project.directory)
        stats = file_manager.get_project_stats()

        return jsonify({
            'success': True,
            'stats': stats
        })

    except Exception as e:
        return jsonify({'success': False, 'message': str(e)})

if __name__ == '__main__':
    init_app()
    app.run(debug=True, host='0.0.0.0', port=5000)
